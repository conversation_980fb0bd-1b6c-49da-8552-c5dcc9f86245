using Il2Cpp;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Types;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple API bridge for Lua scripts.
/// </summary>
public static class LuaAPI
{
    private static bool _typesRegistered = false;

    /// <summary>
    /// Register all necessary types with MoonSharp for proper interop.
    /// This should be called once before creating any scripts.
    /// </summary>
    public static void RegisterTypes()
    {
        if (_typesRegistered) return;

        try
        {
            // Unity basic types
            UserData.RegisterType<Vector3>();
            UserData.RegisterType<Transform>();
            UserData.RegisterType<GameObject>();
            UserData.RegisterType<Camera>();

            // Unity coroutine types
            UserData.RegisterType<UnityEngine.WaitForSeconds>();
            UserData.RegisterType<UnityEngine.WaitForFixedUpdate>();
            UserData.RegisterType<UnityEngine.WaitForEndOfFrame>();
            UserData.RegisterType<UnityEngine.WaitUntil>();
            UserData.RegisterType<UnityEngine.WaitWhile>();
            UserData.RegisterType<UnityEngine.YieldInstruction>();
            UserData.RegisterType<UnityEngine.CustomYieldInstruction>();

            // Game-specific types that might be exposed to Lua
            UserData.RegisterType<LocalPlayer>();
            UserData.RegisterType<Enemy>();
            UserData.RegisterType<GroundItem>();
            UserData.RegisterType<WorldObjectClickListener>();

            // Collections (if needed)
            UserData.RegisterType<List<Enemy>>();
            UserData.RegisterType<List<GroundItem>>();
            UserData.RegisterType<List<WorldObjectClickListener>>();

            _typesRegistered = true;
            MelonLogger.Msg("MoonSharp types registered successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to register MoonSharp types: {ex.Message}");
        }
    }

    public static void RegisterAPI(Script script)
    {
        // Ensure types are registered first
        RegisterTypes();

        // Work WITH MoonSharp - use proper registration methods
        var globals = script.Globals;

        // Game objects - let MoonSharp handle the conversion
        globals["GetPlayer"] = (Func<object?>)(() => PLAYER);
        globals["GetEnemies"] = (Func<object>)(() => ENEMIES);
        globals["GetGroundItems"] = (Func<object>)(() => GROUND_ITEMS);
        globals["GetInteractables"] = (Func<object>)(() => INTERACTABLES);

        // Player functions - return native types, let MoonSharp convert
        globals["GetPlayerPosition"] = (Func<Vector3?>)PlayerHelpers.GetPlayerPosition;
        globals["GetPlayerHealth"] = (Func<float?>)PlayerHelpers.GetPlayerHealth;
        globals["IsPlayerAlive"] = (Func<bool>)PlayerHelpers.IsPlayerAlive;
        globals["IsPlayerInCombat"] = (Func<bool>)(() => false); // Placeholder

        // Utility functions - work WITH MoonSharp's type system
        globals["Log"] = (Action<string>)(msg => MelonLogger.Msg($"[Script] {msg}"));
        globals["Distance"] = (Func<Vector3, Vector3, double>)((v1, v2) => Vector3.Distance(v1, v2));
        globals["Vector3"] = (Func<double, double, double, Vector3>)((x, y, z) => new Vector3((float)x, (float)y, (float)z));

        // Input functions
        globals["GetKey"] = (Func<string, bool>)Input.GetKey;
        globals["GetKeyDown"] = (Func<string, bool>)Input.GetKeyDown;

        // Time functions
        globals["Time"] = (Func<double>)(() => Time.time);
        globals["DeltaTime"] = (Func<double>)(() => Time.deltaTime);
        globals["FrameCount"] = (Func<int>)(() => Time.frameCount);

        // Random
        globals["Random"] = (Func<double>)(() => UnityEngine.Random.value);

        // Math utilities - use native C# functions
        globals["Sqrt"] = (Func<double, double>)Math.Sqrt;
        globals["Sin"] = (Func<double, double>)Math.Sin;
        globals["Cos"] = (Func<double, double>)Math.Cos;
        globals["Abs"] = (Func<double, double>)Math.Abs;
        globals["Min"] = (Func<double, double, double>)Math.Min;
        globals["Max"] = (Func<double, double, double>)Math.Max;

        // Coroutine support functions
        globals["WaitForSeconds"] = (Func<double, object>)(seconds => new UnityEngine.WaitForSeconds((float)seconds));
        globals["WaitForFixedUpdate"] = (Func<object>)(() => new UnityEngine.WaitForFixedUpdate());
        globals["WaitForEndOfFrame"] = (Func<object>)(() => new UnityEngine.WaitForEndOfFrame());
        globals["WaitUntil"] = (Func<Closure, object>)(condition => new UnityEngine.WaitUntil(new Func<bool>(() => {
            try
            {
                var result = condition.Call();
                return result.Boolean;
            }
            catch
            {
                return false;
            }
        })));
        globals["WaitWhile"] = (Func<Closure, object>)(condition => new UnityEngine.WaitWhile(new Func<bool>(() => {
            try
            {
                var result = condition.Call();
                return result.Boolean;
            }
            catch
            {
                return false;
            }
        })));

        // Coroutine creation helper
        globals["CreateCoroutine"] = (Func<Closure, DynValue>)(luaFunction => {
            var coroutine = script.CreateCoroutine(luaFunction);
            return DynValue.NewCoroutine(coroutine);
        });
    }
}
