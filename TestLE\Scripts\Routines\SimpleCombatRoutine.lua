-- Simple Combat Routine in Lua
-- Demonstrates a basic combat task using coroutines

-- Check if we should engage in combat
function CanExecute()
    local enemies = GetEnemies()
    if not enemies or #enemies == 0 then
        return false
    end
    
    -- Check if player is alive
    if not IsPlayerAlive() then
        return false
    end
    
    -- Find closest enemy within range
    local playerPos = GetPlayerPosition()
    if not playerPos then
        return false
    end
    
    local maxRange = 15.0
    for i = 1, #enemies do
        local enemy = enemies[i]
        if enemy and enemy.transform then
            local enemyPos = enemy.transform.position
            local distance = Distance(playerPos, enemyPos)
            if distance <= maxRange then
                return true
            end
        end
    end
    
    return false
end

-- Main combat execution
function Execute()
    return CreateCoroutine(function()
        Log("Starting Simple Combat Routine")
        
        local playerPos = GetPlayerPosition()
        if not playerPos then
            Log("Cannot get player position")
            return
        end
        
        -- Find the closest enemy
        local enemies = GetEnemies()
        local closestEnemy = nil
        local closestDistance = math.huge
        
        for i = 1, #enemies do
            local enemy = enemies[i]
            if enemy and enemy.transform then
                local enemyPos = enemy.transform.position
                local distance = Distance(playerPos, enemyPos)
                if distance < closestDistance then
                    closestDistance = distance
                    closestEnemy = enemy
                end
            end
        end
        
        if not closestEnemy then
            Log("No valid enemy found")
            return
        end
        
        Log("Engaging enemy at distance: " .. closestDistance)
        
        -- Simple combat loop
        local combatStartTime = Time()
        local maxCombatTime = 10.0 -- Max 10 seconds of combat
        
        while IsPlayerAlive() and (Time() - combatStartTime) < maxCombatTime do
            -- Check if enemy is still valid
            if not closestEnemy or not closestEnemy.transform then
                Log("Enemy no longer valid, ending combat")
                break
            end
            
            -- Update enemy position
            local enemyPos = closestEnemy.transform.position
            local currentDistance = Distance(GetPlayerPosition(), enemyPos)
            
            -- If enemy is too far, end combat
            if currentDistance > 20.0 then
                Log("Enemy too far away, ending combat")
                break
            end
            
            -- Simulate combat actions (in a real implementation, you'd call actual combat functions)
            Log("Fighting enemy at distance: " .. currentDistance)
            
            -- Wait a bit between combat actions
            coroutine.yield(WaitForSeconds(0.5))
        end
        
        Log("Combat routine completed")
    end)
end
