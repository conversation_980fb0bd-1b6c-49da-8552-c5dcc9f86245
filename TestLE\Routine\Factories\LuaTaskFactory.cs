using MelonLoader;
using TestLE.Routine.Interfaces;
using TestLE.Routine.Tasks;
using TestLE.Scripting;

namespace TestLE.Routine.Factories;

/// <summary>
/// Factory for creating Lua-based game tasks.
/// Follows KISS principle by automatically discovering and loading Lua routine scripts.
/// </summary>
public class LuaTaskFactory : ITaskFactory
{
    private readonly string _routinesSubfolder;

    public LuaTaskFactory(string routinesSubfolder = "Routines")
    {
        _routinesSubfolder = routinesSubfolder;
    }

    public IEnumerable<IGameTask> CreateTasks()
    {
        var tasks = new List<IGameTask>();

        try
        {
            // Ensure Lua manager is initialized
            if (!LuaManager.Instance.IsInitialized)
            {
                LuaManager.Instance.Initialize();
            }

            // Get all Lua scripts in the routines subfolder
            var routineScripts = GetRoutineScripts();
            
            foreach (var scriptName in routineScripts)
            {
                try
                {
                    // Load the script if not already loaded
                    if (!LuaManager.Instance.Scripts.ContainsKey(scriptName))
                    {
                        if (!LuaManager.Instance.LoadScript(scriptName))
                        {
                            MelonLogger.Warning($"Failed to load Lua routine script: {scriptName}");
                            continue;
                        }
                    }

                    // Create a LuaRoutine task for this script
                    var luaTask = new LuaRoutine(scriptName);
                    tasks.Add(luaTask);
                    
                    MelonLogger.Msg($"Loaded Lua routine: {scriptName}");
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error creating Lua task for {scriptName}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in LuaTaskFactory.CreateTasks: {ex.Message}");
        }

        return tasks;
    }

    private List<string> GetRoutineScripts()
    {
        var routineScripts = new List<string>();
        
        try
        {
            var scriptsDirectory = Path.Combine(
                MelonLoader.Utils.MelonEnvironment.ModsDirectory, 
                "TestLE", 
                "Scripts", 
                _routinesSubfolder
            );

            if (!Directory.Exists(scriptsDirectory))
            {
                Directory.CreateDirectory(scriptsDirectory);
                MelonLogger.Msg($"Created Lua routines directory: {scriptsDirectory}");
                return routineScripts;
            }

            var luaFiles = Directory.GetFiles(scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);
            foreach (var filePath in luaFiles)
            {
                var scriptName = Path.GetFileNameWithoutExtension(filePath);
                if (!string.IsNullOrEmpty(scriptName))
                {
                    // For routines, we need to use the relative path from Scripts folder
                    var relativeName = Path.Combine(_routinesSubfolder, scriptName).Replace('\\', '/');
                    routineScripts.Add(relativeName);
                }
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting routine scripts: {ex.Message}");
        }

        return routineScripts;
    }
}
