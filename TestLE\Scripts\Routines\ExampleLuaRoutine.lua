-- Example Lua Routine demonstrating coroutine usage
-- This routine shows how to create a simple task with coroutines

local lastExecuteTime = 0
local executionInterval = 5.0 -- Execute every 5 seconds

-- Check if this routine can execute
function CanExecute()
    local currentTime = Time()
    return (currentTime - lastExecuteTime) >= executionInterval
end

-- Main execution function - must return a coroutine
function Execute()
    return CreateCoroutine(function()
        Log("Starting Example Lua Routine")
        lastExecuteTime = Time()
        
        -- Get player position
        local playerPos = GetPlayerPosition()
        if playerPos then
            Log("Player position: " .. playerPos.x .. ", " .. playerPos.y .. ", " .. playerPos.z)
        end
        
        -- Wait for 1 second
        coroutine.yield(WaitForSeconds(1.0))
        
        -- Check player health
        local health = GetPlayerHealth()
        if health then
            Log("Player health: " .. health)
        end
        
        -- Wait for another second
        coroutine.yield(WaitForSeconds(1.0))
        
        -- Get nearby enemies
        local enemies = GetEnemies()
        if enemies and #enemies > 0 then
            Log("Found " .. #enemies .. " enemies nearby")
        else
            Log("No enemies found")
        end
        
        -- Wait until next frame
        coroutine.yield()
        
        Log("Example Lua Routine completed")
    end)
end
