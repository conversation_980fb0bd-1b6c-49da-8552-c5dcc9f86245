using System.Collections;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Routine.Interfaces;
using TestLE.Scripting;

namespace TestLE.Routine.Tasks;

/// <summary>
/// A routine task that executes Lua scripts with coroutine support.
/// Follows KISS principle by providing simple Lua-to-coroutine bridge.
/// </summary>
public class LuaRoutine : IGameTask
{
    private readonly string _scriptName;
    private readonly string _canExecuteFunction;
    private readonly string _executeFunction;
    private Script? _script;
    private IEnumerator? _currentCoroutine;

    public LuaRoutine(string scriptName, string canExecuteFunction = "CanExecute", string executeFunction = "Execute")
    {
        _scriptName = scriptName;
        _canExecuteFunction = canExecuteFunction;
        _executeFunction = executeFunction;
    }

    public bool CanExecute()
    {
        EnsureScriptLoaded();
        if (_script == null) return false;

        try
        {
            var result = LuaManager.Instance.CallFunction(_scriptName, _canExecuteFunction);
            return result?.Boolean ?? false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in Lua CanExecute for {_scriptName}: {ex.Message}");
            return false;
        }
    }

    public IEnumerator Execute()
    {
        EnsureScriptLoaded();
        if (_script == null) yield break;

        try
        {
            // Call the Lua execute function which should return a coroutine
            var result = LuaManager.Instance.CallFunction(_scriptName, _executeFunction);
            if (result?.Type == DataType.Thread)
            {
                // Convert Lua coroutine to C# IEnumerator
                var luaCoroutine = result.Coroutine;
                _currentCoroutine = LuaCoroutineToEnumerator(luaCoroutine);
                yield return _currentCoroutine;
            }
            else
            {
                MelonLogger.Warning($"Lua script {_scriptName}.{_executeFunction} did not return a coroutine");
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing Lua routine {_scriptName}: {ex.Message}");
        }
    }

    private void EnsureScriptLoaded()
    {
        if (_script == null && LuaManager.Instance.Scripts.TryGetValue(_scriptName, out var loadedScript))
        {
            _script = loadedScript;
        }
        else if (_script == null)
        {
            // Try to load the script
            LuaManager.Instance.LoadScript(_scriptName);
            LuaManager.Instance.Scripts.TryGetValue(_scriptName, out _script);
        }
    }

    private IEnumerator LuaCoroutineToEnumerator(Coroutine luaCoroutine)
    {
        while (luaCoroutine.State == CoroutineState.Suspended || luaCoroutine.State == CoroutineState.Running)
        {
            try
            {
                var result = luaCoroutine.Resume();
                
                // Handle different yield types
                if (result.Type == DataType.UserData)
                {
                    // Check if it's a Unity yield instruction
                    var userData = result.UserData.Object;
                    if (userData is YieldInstruction yieldInstruction)
                    {
                        yield return yieldInstruction;
                    }
                    else if (userData is CustomYieldInstruction customYield)
                    {
                        yield return customYield;
                    }
                    else
                    {
                        // Default frame wait
                        yield return null;
                    }
                }
                else if (result.Type == DataType.Number)
                {
                    // Treat numbers as wait time in seconds
                    yield return new UnityEngine.WaitForSeconds((float)result.Number);
                }
                else
                {
                    // Default frame wait
                    yield return null;
                }
            }
            catch (ScriptRuntimeException ex)
            {
                MelonLogger.Error($"Lua coroutine error in {_scriptName}: {ex.DecoratedMessage}");
                yield break;
            }
        }
    }
}
