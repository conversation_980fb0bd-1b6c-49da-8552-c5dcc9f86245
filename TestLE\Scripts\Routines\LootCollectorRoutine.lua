-- Loot Collector Routine in Lua
-- Demonstrates advanced coroutine usage with conditional waits

-- Check if there's loot to collect
function CanExecute()
    local groundItems = GetGroundItems()
    return groundItems and #groundItems > 0
end

-- Main loot collection execution
function Execute()
    return CreateCoroutine(function()
        Log("Starting Loot Collector Routine")
        
        local groundItems = GetGroundItems()
        if not groundItems or #groundItems == 0 then
            Log("No ground items found")
            return
        end
        
        Log("Found " .. #groundItems .. " ground items")
        
        local playerPos = GetPlayerPosition()
        if not playerPos then
            Log("Cannot get player position")
            return
        end
        
        -- Sort items by distance (closest first)
        local itemsWithDistance = {}
        for i = 1, #groundItems do
            local item = groundItems[i]
            if item and item.transform then
                local itemPos = item.transform.position
                local distance = Distance(playerPos, itemPos)
                table.insert(itemsWithDistance, {item = item, distance = distance})
            end
        end
        
        -- Sort by distance
        table.sort(itemsWithDistance, function(a, b) return a.distance < b.distance end)
        
        -- Collect items one by one
        for i = 1, #itemsWithDistance do
            local itemData = itemsWithDistance[i]
            local item = itemData.item
            local distance = itemData.distance
            
            Log("Collecting item " .. i .. " at distance: " .. distance)
            
            -- Wait until we're close enough to the item
            coroutine.yield(WaitUntil(function()
                local currentPlayerPos = GetPlayerPosition()
                if not currentPlayerPos or not item.transform then
                    return true -- Stop waiting if something is invalid
                end
                
                local currentDistance = Distance(currentPlayerPos, item.transform.position)
                return currentDistance <= 2.0 -- Close enough to collect
            end))
            
            -- Simulate item collection (in real implementation, you'd call actual collection functions)
            Log("Collected item!")
            
            -- Wait a bit between collections
            coroutine.yield(WaitForSeconds(0.2))
            
            -- Check if we should continue (player still alive, etc.)
            if not IsPlayerAlive() then
                Log("Player died, stopping loot collection")
                break
            end
        end
        
        Log("Loot collection routine completed")
    end)
end
